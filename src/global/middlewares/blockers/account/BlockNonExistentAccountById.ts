import { Request, Response, NextFunction } from 'express';
import { readAccountById } from '../../../../components/account/dals';
import { Var } from '../../../var';

export const BlockNonExistentAccountById = async (req: Request, res: Response, next: NextFunction) => {
  let account = await readAccountById(res.locals.accountId);

  if (!account) {
    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.failure} [DEV] ${res.locals.accountId} is not registered or invalid`);
    }

    // Clear the invalid session
    req.session!.destroy((error: Error) => {
      if (error) {
        // DEBUG: Console output in development only
        if (Var.node.env === 'dev') {
          console.error(`${Var.app.emoji.failure} [DEV] Error destroying invalid session:`, error);
        }
      } else {
        // DEBUG: Console output in development only
        if (Var.node.env === 'dev') {
          console.log(`${Var.app.emoji.success} [DEV] Invalid session destroyed`);
        }
      }

      // Clear the session cookie
      res.clearCookie(Var.node.express.session.name);

      return res.status(200).json({
        success: false,
        message: `${Var.app.emoji.failure} Invalid session. Please log in again.`,
      });
    });

    return; // Early return to prevent next() from being called after the async destroy
  }

  console.log(`${Var.app.emoji.success} ${res.locals.accountId} is registered`);
  res.locals.account = account;
  next();
};
